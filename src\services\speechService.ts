import { getSpeechToTextWsUrl } from './misc';

// 语音识别服务
export interface SpeechRecognitionResult {
  transcript: string;
  confidence: number;
  isFinal: boolean;
  isReplace?: boolean; // 是否替换之前的结果
  pgs?: 'apd' | 'rpl'; // 科大讯飞动态修正标识：apd=追加，rpl=替换
  rg?: number[]; // 替换范围，仅当pgs='rpl'时有效
}

export interface SpeechRecognitionOptions {
  language?: string;
  continuous?: boolean;
  interimResults?: boolean;
  maxAlternatives?: number;
  noSpeechTimeout?: number; // 无语音超时时间（毫秒）
}

export interface CountdownInfo {
  remainingSeconds: number;
  totalSeconds: number;
}

export class SpeechRecognitionService {
  private websocket: WebSocket | null = null;
  private mediaRecorder: MediaRecorder | null = null;
  private audioContext: AudioContext | null = null;
  private audioSource: MediaStreamAudioSourceNode | null = null;
  private audioProcessor: AudioNode | null = null;
  private mediaStream: MediaStream | null = null;
  private isSupported = false;
  private isListening = false;
  private isPreparing = false; // 准备中状态
  private shouldRestart = false; // 是否应该自动重启
  private restartTimeout: NodeJS.Timeout | null = null;
  private noSpeechTimeout: NodeJS.Timeout | null = null; // 无语音超时定时器
  private countdownInterval: NodeJS.Timeout | null = null; // 倒计时定时器
  private noSpeechTimeoutDuration = 5000; // 默认5秒无语音超时
  private countdownStartTime: number = 0; // 倒计时开始时间
  private isCountingDown = false; // 是否正在倒计时
  private wsUrl = '';
  private appId = '';
  private currentCallbacks: {
    onResult?: (result: SpeechRecognitionResult) => void;
    onError?: (error: string) => void;
    onEnd?: () => void;
    onPreparing?: () => void; // 准备中回调
    onListening?: () => void; // 开始监听回调
    onCountdown?: (info: CountdownInfo | null) => void; // 倒计时回调，null表示清除倒计时
    onAudioData?: () => void; // 音频数据检测回调，当检测到音频数据时触发
  } = {};

  private resetNoSpeechTimeout() {
    // 清除现有的超时和倒计时
    if (this.noSpeechTimeout) {
      clearTimeout(this.noSpeechTimeout);
      this.noSpeechTimeout = null;
    }
    this.clearCountdown();

    // 只有在正常监听状态下才设置无语音超时
    if (this.isListening && this.shouldRestart && !this.isCountingDown) {
      this.noSpeechTimeout = setTimeout(() => {
        // 再次检查状态，确保仍在监听且应该重启
        if (this.isListening && this.shouldRestart && !this.isCountingDown) {
          this.startCountdown();
        }
      }, this.noSpeechTimeoutDuration);
    }
  }

  private startCountdown() {
    // 如果已经在倒计时，先清除
    if (this.isCountingDown) {
      this.clearCountdown();
    }
    
    this.isCountingDown = true;
    this.countdownStartTime = Date.now();
    const countdownDuration = 5000; // 5秒倒计时
    const totalSeconds = Math.ceil(countdownDuration / 1000);
    
    // 立即触发第一次倒计时回调
    this.currentCallbacks.onCountdown?.({
      remainingSeconds: totalSeconds,
      totalSeconds
    });

    this.countdownInterval = setInterval(() => {
      // 双重检查，确保倒计时状态正确
      if (!this.isCountingDown || !this.isListening) {
        this.clearCountdown();
        return;
      }

      const elapsed = Date.now() - this.countdownStartTime;
      const remaining = Math.max(0, countdownDuration - elapsed);
      const remainingSeconds = Math.ceil(remaining / 1000);

      if (remainingSeconds > 0) {
        this.currentCallbacks.onCountdown?.({
          remainingSeconds,
          totalSeconds
        });
      } else {
        // 倒计时结束，彻底停止识别
        this.endCountdownAndStop();
      }
    }, 1000);
  }

  private clearCountdown() {
    this.isCountingDown = false;
    this.countdownStartTime = 0;
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
      this.countdownInterval = null;
    }
  }

  private endCountdownAndStop() {
    // 清除倒计时
    this.clearCountdown();

    // 设置标志，防止重启
    this.shouldRestart = false;
    this.isListening = false;
    this.isPreparing = false;

    // 通知UI倒计时结束
    this.currentCallbacks.onCountdown?.(null);

    // 完全清理所有资源，包括WebSocket
    this.cleanup();

    // 通知UI识别结束
    this.currentCallbacks.onEnd?.();
  }

  private clearTimeouts() {
    if (this.restartTimeout) {
      clearTimeout(this.restartTimeout);
      this.restartTimeout = null;
    }
    if (this.noSpeechTimeout) {
      clearTimeout(this.noSpeechTimeout);
      this.noSpeechTimeout = null;
    }
    this.clearCountdown();
  }

  constructor() {
    this.initializeRecognition();
  }

  private initializeRecognition() {
    // 检查浏览器是否支持WebRTC和WebSocket
    this.isSupported = !!(navigator.mediaDevices && 
                         typeof navigator.mediaDevices.getUserMedia === 'function' && 
                         window.WebSocket && 
                         window.MediaRecorder);
  }

  private async initializeWebSocket(): Promise<void> {
    try {
      // 获取WebSocket URL和AppId
      const { wsUrl, appId } = await getSpeechToTextWsUrl();
      this.wsUrl = wsUrl;
      this.appId = appId;
      
      // 创建WebSocket连接
      this.websocket = new WebSocket(wsUrl);
      
      this.websocket.onopen = () => {
        console.log('科大讯飞WebSocket连接已建立');
        // 连接建立后，音频数据的发送将在MediaRecorder的onstart中处理
      };
      
      this.websocket.onmessage = (event) => {
        this.handleWebSocketMessage(event.data);
      };
      
      this.websocket.onerror = (error) => {
        console.error('科大讯飞WebSocket错误:', error);
        this.currentCallbacks.onError?.('语音识别连接错误');
        this.cleanup();
      };
      
      this.websocket.onclose = () => {
        console.log('科大讯飞WebSocket连接已关闭');
        if (this.shouldRestart && this.isListening) {
          this.restartRecognition();
        } else {
          this.cleanup();
          this.currentCallbacks.onEnd?.();
        }
      };
      
      // 等待连接建立
      await new Promise((resolve, reject) => {
        if (this.websocket) {
          const openHandler = () => {
            this.websocket?.removeEventListener('open', openHandler);
            this.websocket?.removeEventListener('error', errorHandler);
            resolve(undefined);
          };
          
          const errorHandler = (error: Event) => {
            this.websocket?.removeEventListener('open', openHandler);
            this.websocket?.removeEventListener('error', errorHandler);
            reject(error);
          };
          
          this.websocket.addEventListener('open', openHandler);
          this.websocket.addEventListener('error', errorHandler);
          
          // 添加超时保护
          setTimeout(() => {
            if (this.websocket?.readyState !== WebSocket.OPEN) {
              this.websocket?.removeEventListener('open', openHandler);
              this.websocket?.removeEventListener('error', errorHandler);
              reject(new Error('WebSocket连接超时'));
            }
          }, 10000); // 10秒超时
        } else {
          reject(new Error('WebSocket创建失败'));
        }
      });
      
      console.log('WebSocket连接已完全建立，状态:', this.websocket.readyState);
      
    } catch (error) {
      console.error('初始化WebSocket失败:', error);
      throw new Error('无法连接到语音识别服务');
    }
  }

  private handleWebSocketMessage(data: string): void {
    try {
      const message = JSON.parse(data);
      console.log('收到WebSocket消息:', message);

      // 科大讯飞语音听写返回格式
      if (message.code === 0 && message.data) {
        const result = message.data;
        if (result.result) {
          const ws = result.result.ws;
          if (ws && ws.length > 0) {
            let transcript = '';
            let confidence = 0;

            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            ws.forEach((word: any) => {
              if (word.cw && word.cw.length > 0) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                word.cw.forEach((char: any) => {
                  transcript += char.w;
                  confidence = Math.max(confidence, char.sc || 0);
                });
              }
            });

            // 科大讯飞动态修正的结果判断逻辑：
            // ls: true 表示最后一个片段（last segment）
            // pgs: "rpl" 表示替换之前的结果，"apd" 表示追加到之前的结果
            const isLastSegment = result.result.ls === true;
            const pgs = result.result.pgs; // 'apd' | 'rpl' | undefined
            const rg = result.result.rg; // 替换范围数组

            // 动态修正模式下的最终结果判断：
            // 1. 如果有pgs字段，说明是动态修正的中间结果，都不是最终结果
            // 2. 只有当ls=true且没有pgs字段时，才是真正的最终结果
            const isFinal = isLastSegment && !pgs;

            console.log('解析出文本:', {
              transcript,
              confidence,
              isFinal,
              isLastSegment,
              pgs,
              rg,
              ls: result.result.ls
            });

            if (transcript.trim()) {
              // 重置无语音超时
              if (this.isCountingDown) {
                this.clearCountdown();
                this.currentCallbacks.onCountdown?.(null);
              }
              this.resetNoSpeechTimeout();

              this.currentCallbacks.onResult?.({
                transcript: transcript.trim(),
                confidence: confidence / 100, // 转换为0-1范围
                isFinal,
                isReplace: pgs === 'rpl', // 保持向后兼容
                pgs,
                rg
              });
            }
          }
        }
      } else if (message.code !== 0) {
        // 错误处理
        console.error('科大讯飞返回错误:', message);
        this.currentCallbacks.onError?.(message.message || '语音识别错误');
        this.cleanup();
      }
    } catch (error) {
      console.error('解析WebSocket消息失败:', error);
    }
  }

  private async initializeAudioCapture(): Promise<void> {
    try {
      console.log('开始初始化音频捕获...');
      
      // 确保WebSocket连接已建立
      if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
        throw new Error('WebSocket连接未建立，无法开始音频捕获');
      }
      
      // 获取麦克风权限
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      });
      
      console.log('成功获取媒体流:', this.mediaStream);
      
      // 创建AudioContext
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: 16000
      });
      
      console.log('创建音频上下文，采样率:', this.audioContext.sampleRate);
      
      // 创建音频源
      this.audioSource = this.audioContext.createMediaStreamSource(this.mediaStream);
      
      // 尝试使用现代的AudioWorkletNode
      let processor: AudioNode;
      
      try {
        // 首先尝试使用AudioWorkletNode
        await this.setupAudioWorklet();
        console.log('使用AudioWorkletNode处理音频');
      } catch (error) {
        console.warn('AudioWorkletNode不可用，降级到ScriptProcessorNode:', error);
        // 降级到ScriptProcessorNode
        processor = this.audioContext.createScriptProcessor(4096, 1, 1);
        (processor as ScriptProcessorNode).onaudioprocess = (event) => {
          // 只有在正常监听状态下才处理音频数据
          if (!this.isListening) {
            return;
          }

          const inputBuffer = event.inputBuffer;
          const inputData = inputBuffer.getChannelData(0);

          // 转换为16位PCM
          const pcmData = new Int16Array(inputData.length);
          for (let i = 0; i < inputData.length; i++) {
            const sample = Math.max(-1, Math.min(1, inputData[i]));
            pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
          }

          // 发送音频数据
          this.sendAudioData(pcmData);
        };
        
        // 连接音频节点
        this.audioSource.connect(processor);
        processor.connect(this.audioContext.destination);
        this.audioProcessor = processor;
      }
      
      // 发送开始帧
      this.sendStartFrame();
      
      this.isPreparing = false;
      this.isListening = true;
      this.resetNoSpeechTimeout();
      this.currentCallbacks.onListening?.();
      
      console.log('音频捕获初始化完成');
      
    } catch (error) {
      console.error('初始化音频捕获失败:', error);
      throw new Error('无法访问麦克风');
    }
  }

  private async setupAudioWorklet(): Promise<void> {
    if (!this.audioContext || !this.audioSource) {
      throw new Error('AudioContext或AudioSource未初始化');
    }

    // 创建AudioWorklet处理器的代码
    const workletCode = `
      class AudioProcessor extends AudioWorkletProcessor {
        process(inputs, outputs, parameters) {
          const input = inputs[0];
          if (input.length > 0) {
            const inputData = input[0];
            
            // 转换为16位PCM
            const pcmData = new Int16Array(inputData.length);
            for (let i = 0; i < inputData.length; i++) {
              const sample = Math.max(-1, Math.min(1, inputData[i]));
              pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
            }
            
            // 发送音频数据到主线程
            this.port.postMessage(pcmData);
          }
          
          return true;
        }
      }
      
      registerProcessor('audio-processor', AudioProcessor);
    `;

    // 创建Blob URL
    const blob = new Blob([workletCode], { type: 'application/javascript' });
    const workletUrl = URL.createObjectURL(blob);

    try {
      // 添加AudioWorklet模块
      await this.audioContext.audioWorklet.addModule(workletUrl);
      
      // 创建AudioWorkletNode
      const workletNode = new AudioWorkletNode(this.audioContext, 'audio-processor');
      
      // 监听来自AudioWorklet的消息
      workletNode.port.onmessage = (event) => {
        const pcmData = event.data;
        // 只有在正常监听状态下才处理音频数据
        if (this.isListening) {
          this.sendAudioData(pcmData);
        }
      };
      
      // 连接音频节点
      this.audioSource.connect(workletNode);
      this.audioProcessor = workletNode;
      
      // 清理Blob URL
      URL.revokeObjectURL(workletUrl);
      
    } catch (error) {
      URL.revokeObjectURL(workletUrl);
      throw error;
    }
  }

  private sendAudioData(pcmData: Int16Array): void {
    // 检测音频数据是否有有效信号（简单的音量检测）
    let hasSignal = false;
    const threshold = 100; // 音量阈值，可以根据需要调整
    for (let i = 0; i < pcmData.length; i++) {
      if (Math.abs(pcmData[i]) > threshold) {
        hasSignal = true;
        break;
      }
    }

    // 如果检测到音频信号，触发回调（无论WebSocket是否连接）
    if (hasSignal) {
      this.currentCallbacks.onAudioData?.();
    }

    // 检查WebSocket连接状态，如果未连接则静默跳过（不打印警告）
    if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN || !this.appId) {
      return;
    }

    if (!pcmData || pcmData.length === 0) {
      return;
    }

    try {
      // 将PCM数据转换为base64
      const uint8Array = new Uint8Array(pcmData.buffer);
      const base64Audio = btoa(String.fromCharCode(...uint8Array));

      // 科大讯飞语音听写完整数据格式
      const audioData = {
        common: {
          app_id: this.appId
        },
        business: {
          language: "zh_cn",
          domain: "iat",
          accent: "mandarin",
          vinfo: 1,
          vad_eos: 10000,
          dwa: "wpgs" // 启用动态修正功能，实现一个字一个字的效果
        },
        data: {
          status: 1, // 0: 第一帧音频，1: 中间音频，2: 最后一帧音频
          format: "audio/L16;rate=16000", // 音频格式
          audio: base64Audio,
          encoding: "raw"
        }
      };

      this.websocket.send(JSON.stringify(audioData));

      // 只在调试模式下输出详细日志
      if (console.debug) {
        console.debug('发送音频数据:', {
          pcmLength: pcmData.length,
          base64Length: base64Audio.length,
          appId: this.appId,
          hasSignal
        });
      }
    } catch (error) {
      console.error('发送音频数据失败:', error);
      // 不要因为单次发送失败就停止整个识别过程
    }
  }

  private sendStartFrame(): void {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      // 发送开始帧 - 科大讯飞完整格式
      const startData = {
        common: {
          app_id: this.appId
        },
        business: {
          language: "zh_cn",
          domain: "iat",
          accent: "mandarin",
          vinfo: 1,
          vad_eos: 10000,
          dwa: "wpgs" // 启用动态修正功能，实现一个字一个字的效果
        },
        data: {
          status: 0, // 第一帧
          format: "audio/L16;rate=16000",
          audio: "",
          encoding: "raw"
        }
      };
      
      this.websocket.send(JSON.stringify(startData));
    }
  }

  private sendEndFrame(): void {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      // 发送结束帧 - 科大讯飞完整格式
      const endData = {
        common: {
          app_id: this.appId
        },
        business: {
          language: "zh_cn",
          domain: "iat",
          accent: "mandarin",
          vinfo: 1,
          vad_eos: 10000
        },
        data: {
          status: 2, // 最后一帧
          format: "audio/L16;rate=16000",
          audio: "",
          encoding: "raw"
        }
      };
      
      this.websocket.send(JSON.stringify(endData));
    }
  }

  private cleanup(): void {
    this.isListening = false;
    this.isPreparing = false;
    this.shouldRestart = false;
    this.clearTimeouts();
    
    // 清理音频处理器
    if (this.audioProcessor) {
      this.audioProcessor.disconnect();
      this.audioProcessor = null;
    }
    
    // 清理音频源
    if (this.audioSource) {
      this.audioSource.disconnect();
      this.audioSource = null;
    }
    
    // 清理媒体流
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
      this.mediaStream = null;
    }
    
    // 清理MediaRecorder（如果还在使用）
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }
    
    // 清理WebSocket
    if (this.websocket) {
      // 移除事件监听器，防止在关闭过程中触发onclose事件导致重启
      this.websocket.onclose = null;
      this.websocket.onerror = null;
      this.websocket.onmessage = null;

      if (this.websocket.readyState === WebSocket.OPEN) {
        // 发送结束帧
        this.sendEndFrame();
        this.websocket.close();
      }
    }
    
    // 清理AudioContext
    if (this.audioContext) {
      this.audioContext.close();
    }
    
    this.mediaRecorder = null;
    this.websocket = null;
    this.audioContext = null;
  }

  public isRecognitionSupported(): boolean {
    return this.isSupported;
  }

  public isCurrentlyListening(): boolean {
    return this.isListening;
  }

  public isCurrentlyPreparing(): boolean {
    return this.isPreparing;
  }

  private restartRecognition() {
    if (this.restartTimeout) {
      clearTimeout(this.restartTimeout);
    }
    
    // 短暂延迟后重启，避免频繁重启
    this.restartTimeout = setTimeout(async () => {
      if (this.shouldRestart) {
        try {
          await this.initializeWebSocket();
          await this.initializeAudioCapture();
          
          // 音频捕获在initializeAudioCapture中已经开始
          
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (error) {
          // 如果重启失败，停止尝试
          this.currentCallbacks.onError?.('语音识别重启失败');
          this.cleanup();
        }
      }
    }, 1000);
  }

  public async startListening(
    onResult: (result: SpeechRecognitionResult) => void,
    onError?: (error: string) => void,
    onEnd?: () => void,
    onPreparing?: () => void,
    onListening?: () => void,
    options?: SpeechRecognitionOptions,
    onCountdown?: (info: CountdownInfo | null) => void,
    onAudioData?: () => void
  ): Promise<void> {
    if (!this.isSupported) {
      onError?.('浏览器不支持语音识别功能');
      return;
    }

    if (this.isListening || this.isPreparing) {
      onError?.('语音识别已在进行中');
      return;
    }

    // 保存回调函数
    this.currentCallbacks = { onResult, onError, onEnd, onPreparing, onListening, onCountdown, onAudioData };

    // 应用配置选项
    if (options?.noSpeechTimeout !== undefined) {
      this.noSpeechTimeoutDuration = options.noSpeechTimeout;
    }

    // 启用自动重启
    this.shouldRestart = true;

    // 进入准备状态
    this.isPreparing = true;
    this.currentCallbacks.onPreparing?.();

    try {
      // 初始化WebSocket连接
      await this.initializeWebSocket();

      // 初始化音频捕获
      await this.initializeAudioCapture();

      // 音频捕获在initializeAudioCapture中已经开始

    } catch (error) {
      this.cleanup();
      onError?.(error instanceof Error ? error.message : '启动语音识别失败');
    }
  }

  public stopListening(): void {
    this.shouldRestart = false;
    this.cleanup();
  }

  public abortListening(): void {
    this.shouldRestart = false;
    this.cleanup();
  }
}

// 创建单例实例
export const speechRecognitionService = new SpeechRecognitionService();