# 科大讯飞语音识别动态修正功能优化

## 概述

基于科大讯飞语音识别WebSocket API的最佳实践，我们对语音识别功能进行了重大优化，实现了一个字一个字的动态显示效果，并支持动态修正功能。

## 主要改进

### 1. 启用科大讯飞动态修正功能

在 `src/services/speechService.ts` 中：

```typescript
business: {
  language: "zh_cn",
  domain: "iat",
  accent: "mandarin",
  vinfo: 1,
  vad_eos: 10000,
  dwa: "wpgs" // 启用动态修正功能，实现一个字一个字的效果
}
```

### 2. 增强语音识别结果接口

扩展了 `SpeechRecognitionResult` 接口，支持动态修正参数：

```typescript
export interface SpeechRecognitionResult {
  transcript: string;
  confidence: number;
  isFinal: boolean;
  isReplace?: boolean; // 是否替换之前的结果
  pgs?: 'apd' | 'rpl'; // 科大讯飞动态修正标识：apd=追加，rpl=替换
  rg?: number[]; // 替换范围，仅当pgs='rpl'时有效
}
```

### 3. 优化WebSocket消息处理逻辑

重新实现了 `handleWebSocketMessage` 方法，正确处理动态修正的结果：

- **apd (append)**: 追加模式，将新识别的文字追加到已有结果后面
- **rpl (replace)**: 替换模式，替换指定范围的之前识别结果
- **最终结果判断**: 只有当 `ls=true` 且没有 `pgs` 字段时，才是真正的最终结果

### 4. 实现动态文本管理

在 `src/pages/Scene.tsx` 中添加了新的状态管理：

```typescript
// 动态修正相关状态
const [speechResultText, setSpeechResultText] = useState(''); // 已确认的语音识别结果
const speechResultTextRef = useRef(''); // 已确认结果的ref
const [speechTempText, setSpeechTempText] = useState(''); // 临时的语音识别结果
const speechTempTextRef = useRef(''); // 临时结果的ref
```

### 5. 重构语音识别文本回调

实现了支持动态修正的文本处理逻辑：

```typescript
const handleSpeechTranscript = useCallback((result: { 
  transcript: string; 
  isFinal: boolean; 
  pgs?: 'apd' | 'rpl'; 
  rg?: number[] 
}) => {
  // 处理动态修正逻辑
  if (pgs === 'apd') {
    // 追加模式：将临时文字同步到已确认结果
  } else if (pgs === 'rpl') {
    // 替换模式：替换指定范围的结果
  } else {
    // 普通临时结果
  }
}, []);
```

## 技术特点

### 1. 一个字一个字的显示效果

通过启用科大讯飞的 `dwa: "wpgs"` 参数，实现了更细粒度的识别结果返回，用户可以看到文字一个一个地出现，提供更好的视觉反馈。

### 2. 智能动态修正

- **实时修正**: 当用户说话时，系统会实时修正之前可能识别错误的文字
- **追加模式**: 新识别的文字会追加到已确认的结果后面
- **替换模式**: 系统会智能替换之前识别错误的部分

### 3. 精确的光标位置管理

- 在语音识别开始时记录当前光标位置
- 在光标位置动态插入识别的文字
- 识别结束后将光标定位到插入文字的末尾

### 4. 状态管理优化

- 使用 React ref 避免状态更新时机问题
- 分离已确认结果和临时结果的管理
- 提供完整的状态清理机制

## 用户体验改进

1. **实时反馈**: 用户可以看到文字一个字一个字地出现
2. **智能修正**: 系统会自动修正识别错误，无需用户手动编辑
3. **精确插入**: 文字会准确插入到TextArea的当前光标位置
4. **流畅体验**: 优化了状态管理，避免了界面闪烁和卡顿

## 兼容性

- 保持了与现有代码的向后兼容性
- 支持旧的调用方式，同时提供新的动态修正功能
- 在不支持动态修正的情况下，会降级到普通识别模式

## 最佳实践参考

本实现基于科大讯飞官方文档和最佳实践：

1. [语音听写（流式版）WebAPI 文档](https://www.xfyun.cn/doc/asr/voicedictation/API.html)
2. 动态修正功能的正确使用方法
3. WebSocket连接和音频数据传输的优化
4. 错误处理和状态管理的最佳实践

## 测试建议

1. 测试普通语音识别功能
2. 测试动态修正效果（说话时观察文字的实时变化）
3. 测试在TextArea不同位置插入文字
4. 测试长时间语音识别的稳定性
5. 测试网络异常情况下的错误处理

通过这些优化，我们实现了更加智能和用户友好的语音识别体验，符合现代语音输入应用的标准。
