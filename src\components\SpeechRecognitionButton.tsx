import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Tooltip, Badge, message } from 'antd';
import { AudioOutlined, LoadingOutlined } from '@ant-design/icons';
import { speechRecognitionService, type CountdownInfo } from '../services/speechService';

interface SpeechRecognitionButtonProps {
  onTranscript: (result: { transcript: string; isFinal: boolean; pgs?: 'apd' | 'rpl'; rg?: number[] }) => void; // 支持动态修正的回调
  onStateChange?: (state: 'idle' | 'preparing' | 'listening') => void; // 简化：统一的状态回调
  className?: string;
}

const SpeechRecognitionButton: React.FC<SpeechRecognitionButtonProps> = ({
  onTranscript,
  onStateChange,
  className = ''
}) => {
  const [isSupported, setIsSupported] = useState(false);
  const [currentState, setCurrentState] = useState<'idle' | 'preparing' | 'listening'>('idle');
  const [countdownInfo, setCountdownInfo] = useState<CountdownInfo | null>(null);
  const [hasAudioDetected, setHasAudioDetected] = useState(false);

  useEffect(() => {
    setIsSupported(speechRecognitionService.isRecognitionSupported());
  }, []);

  // 状态变化时通知外部（移除onStateChange依赖，避免重复调用）
  useEffect(() => {
    onStateChange?.(currentState);
  }, [currentState]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleStartListening = async () => {
    if (currentState !== 'idle') {
      // 如果正在监听或准备中，点击停止
      speechRecognitionService.stopListening();
      setCurrentState('idle');
      setCountdownInfo(null);
      setHasAudioDetected(false);
      return;
    }

    try {
      await speechRecognitionService.startListening(
        (result) => {
          // 传递完整的result对象，支持动态修正
          onTranscript({
            transcript: result.transcript,
            isFinal: result.isFinal,
            pgs: result.pgs,
            rg: result.rg
          });

          // 如果有音频输入，更新状态
          if (result.transcript.trim()) {
            setHasAudioDetected(true);
            if (countdownInfo) {
              setCountdownInfo(null);
            }
          }
        },
        (error) => {
          message.error(error);
          setCurrentState('idle');
          setCountdownInfo(null);
          setHasAudioDetected(false);
        },
        () => {
          // 识别结束
          setCurrentState('idle');
          setCountdownInfo(null);
          setHasAudioDetected(false);
        },
        () => {
          // 准备中状态
          setCurrentState('preparing');
          setCountdownInfo(null);
          setHasAudioDetected(false);
        },
        () => {
          // 开始监听状态
          setCurrentState('listening');
          setHasAudioDetected(false);
        },
        {
          language: 'zh-CN',
          continuous: true,
          interimResults: true,
          noSpeechTimeout: 5000 // 5秒无语音超时
        },
        (countdown) => {
          // 倒计时回调
          setCountdownInfo(countdown);
          if (countdown) {
            setHasAudioDetected(false);
          }
        },
        () => {
          // 音频数据检测回调
          setHasAudioDetected(true);
          if (countdownInfo) {
            setCountdownInfo(null);
          }
        }
      );
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      message.error('启动语音识别失败');
      setCurrentState('idle');
      setCountdownInfo(null);
      setHasAudioDetected(false);
    }
  };

  // 获取状态文本
  const getStatusText = () => {
    if (currentState === 'preparing') {
      return '准备中';
    }
    if (currentState === 'listening') {
      // 如果有倒计时信息，优先显示倒计时
      if (countdownInfo) {
        return (
          <span>
            识别中：没有检测到声音，将在
            <strong style={{ fontWeight: 'bold', color: '#ff4d4f' }}>
              {countdownInfo.remainingSeconds}
            </strong>
            秒后结束！
          </span>
        );
      }
      return '识别中';
    }
    return '';
  };

  // 获取tooltip文本
  const getTooltipText = () => {
    if (currentState !== 'idle') {
      return '结束语音输入';
    }
    return '语音输入';
  };

  // 动态生成按钮类名
  const getButtonClass = () => {
    let buttonClass = `scene-function-button speech-recognition-button ${className}`;

    if (currentState === 'preparing') {
      buttonClass += ' preparing';
    } else if (currentState === 'listening') {
      buttonClass += ' listening';
    }

    return buttonClass;
  };

  if (!isSupported) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <Tooltip title="浏览器不支持语音识别">
          <Button 
            className={`scene-function-button ${className}`}
            disabled
            icon={<AudioOutlined style={{ color: '#cccccc' }} />}
          />
        </Tooltip>
      </div>
    );
  }

  const statusText = getStatusText();

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <Tooltip title={getTooltipText()}>
        <Badge
          dot={currentState === 'listening' && hasAudioDetected && !countdownInfo}
          color="#52c41a"
        >
          <Button
            className={getButtonClass()}
            onClick={handleStartListening}
            icon={
              currentState === 'preparing' ? (
                <LoadingOutlined />
              ) : (
                <AudioOutlined />
              )
            }
          />
        </Badge>
      </Tooltip>
      {statusText && (
        <div style={{
          fontSize: '12px',
          color: '#666',
          whiteSpace: 'nowrap',
          maxWidth: countdownInfo ? '300px' : '120px',
          overflow: 'hidden',
          textOverflow: 'ellipsis'
        }}>
          {statusText}
        </div>
      )}
    </div>
  );
};

export default SpeechRecognitionButton;